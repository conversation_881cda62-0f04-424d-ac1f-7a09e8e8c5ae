// 页面切换
function showPage(page) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(p => p.classList.add('hidden'));
    
    // 显示指定页面
    if (page === 'home') {
        document.getElementById('homePage').classList.remove('hidden');
    } else if (page === 'profile') {
        document.getElementById('profilePage').classList.remove('hidden');
    }
    
    // 更新导航状态
    document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
    event.target.closest('.nav-item').classList.add('active');
}

// 显示我的优惠券
function showMyCoupons() {
    document.querySelectorAll('.page').forEach(p => p.classList.add('hidden'));
    document.getElementById('couponsPage').classList.remove('hidden');
}

// 显示我的预约
function showMyAppointments() {
    document.querySelectorAll('.page').forEach(p => p.classList.add('hidden'));
    document.getElementById('appointmentsPage').classList.remove('hidden');
}

// 返回上一页
function goBack() {
    document.querySelectorAll('.page').forEach(p => p.classList.add('hidden'));
    document.getElementById('profilePage').classList.remove('hidden');
}

// 优惠券标签切换
function switchTab(type) {
    // 更新标签状态
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');
    
    // 切换内容
    if (type === 'unused') {
        document.getElementById('unusedCoupons').classList.remove('hidden');
        document.getElementById('usedCoupons').classList.add('hidden');
    } else {
        document.getElementById('unusedCoupons').classList.add('hidden');
        document.getElementById('usedCoupons').classList.remove('hidden');
    }
}

// 优惠券领取
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('receive-btn')) {
        e.target.textContent = '已领取';
        e.target.style.background = '#ccc';
        e.target.disabled = true;
        
        // 显示成功提示
        showToast('优惠券领取成功！');
    }
    
    if (e.target.classList.contains('use-btn')) {
        showToast('跳转到使用页面');
    }
    
    if (e.target.classList.contains('consult-btn')) {
        showToast('正在为您接通医生...');
    }
});

// 预约表单提交
document.querySelector('.appointment-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const name = this.querySelector('input[type="text"]').value;
    const phone = this.querySelector('input[type="tel"]').value;
    const condition = this.querySelector('select').value;
    
    if (!name || !phone || !condition) {
        showToast('请填写完整信息');
        return;
    }
    
    // 模拟提交
    showToast('预约提交成功，我们会尽快联系您！');
    this.reset();
});

// 显示提示信息
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 10000;
        animation: fadeInOut 2s ease-in-out;
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(toast);
    
    // 2秒后移除
    setTimeout(() => {
        document.body.removeChild(toast);
        document.head.removeChild(style);
    }, 2000);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 默认显示首页
    showPage('home');
    
    // 添加页面切换动画
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.style.transition = 'opacity 0.3s ease-in-out';
    });
});