const { createApp } = Vue;

createApp({
    data() {
        return {
            activeTab: 'home',
            couponTab: 'unused',
            showConditionPicker: false,
            form: {
                name: '',
                phone: '',
                condition: '',
                description: ''
            },
            conditionOptions: ['白癜风', '银屑病', '湿疹', '荨麻疹', '其他'],
            coupon1: {
                id: 1,
                condition: '皮肤三维皮肤检测仪',
                reason: '',
                value: 0,
                name: '免费检查',
                startAt: Date.now(),
                endAt: Date.now() + 365 * 24 * 60 * 60 * 1000,
                valueDesc: '0',
                unitDesc: '元'
            },
            coupon2: {
                id: 2,
                condition: '美国进口308光斑',
                reason: '',
                value: 78,
                name: '78元体验',
                startAt: Date.now(),
                endAt: Date.now() + 365 * 24 * 60 * 60 * 1000,
                valueDesc: '78',
                unitDesc: '元'
            },
            coupon3: {
                id: 3,
                condition: '美国进口308光斑',
                reason: '',
                value: 180,
                name: '半价优惠',
                startAt: Date.now(),
                endAt: Date.now() + 365 * 24 * 60 * 60 * 1000,
                valueDesc: '半价',
                unitDesc: ''
            },
            unusedCoupons: []
        }
    },
    mounted() {
        this.showPage('home');
        this.unusedCoupons = [
            {
                id: 1,
                condition: '白癜风6维32项基础病因筛查',
                reason: '本次援助最终解释权归浙江益肤所有。',
                value: 0,
                name: '免费检查',
                startAt: Date.now(),
                endAt: Date.now() + 365 * 24 * 60 * 60 * 1000,
                valueDesc: '0',
                unitDesc: '元'
            },
            {
                id: 2,
                condition: '美国进口 308 光斑',
                reason: '1.价值高达360元；2.免费体验 6 个（限初诊）；3.本次援助最终解释权归浙江益肤所有。',
                value: 0,
                name: '免费体验',
                startAt: Date.now(),
                endAt: Date.now() + 365 * 24 * 60 * 60 * 1000,
                valueDesc: '0',
                unitDesc: '元'
            }
        ];
    },
    methods: {
        // 页面切换
        showPage(page) {
            document.querySelectorAll('.page').forEach(p => p.classList.add('hidden'));
            document.getElementById(page + 'Page').classList.remove('hidden');
        },

        // 底部导航切换
        onTabChange(name) {
            this.showPage(name);
        },

        // 显示我的优惠券
        showMyCoupons() {
            this.showPage('coupons');
        },

        // 显示我的预约
        showMyAppointments() {
            this.showPage('appointments');
        },

        // 返回上一页
        goBack() {
            this.showPage('profile');
        },

        // 优惠券标签切换
        onCouponTabChange(name) {
            this.couponTab = name;
        },

        // 优惠券领取
        receiveCoupon(id) {
            this.$toast('优惠券领取成功！');
        },

        // 使用优惠券
        useCoupon(index) {
            this.$toast('跳转到使用页面');
        },

        // 咨询医生
        consultDoctor() {
            this.$toast('正在为您接通医生...');
        },

        // 病情选择确认
        onConditionConfirm({ selectedValues }) {
            this.form.condition = selectedValues[0];
            this.showConditionPicker = false;
        },

        // 表单提交
        onSubmit(values) {
            if (!values.name || !values.phone || !values.condition) {
                this.$toast('请填写完整信息');
                return;
            }

            this.$toast('预约提交成功，我们会尽快联系您！');
            this.form = {
                name: '',
                phone: '',
                condition: '',
                description: ''
            };
        }
    }
}).use(vant).mount('body');