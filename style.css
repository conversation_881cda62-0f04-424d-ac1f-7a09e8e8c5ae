* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background: #f5f5f5;
    padding-bottom: 60px;
    color: #333;
}

.page {
    min-height: 100vh;
    background: #f5f5f5;
}

.hidden {
    display: none;
}

/* Banner */
.banner {
    position: relative;
    height: 280px;
    background: linear-gradient(180deg, #ff4757 0%, #ff3838 100%);
    overflow: hidden;
}

.banner-bg {
    position: relative;
    height: 100%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.banner-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.hospital-logo img {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
}

.banner h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

/* 优惠券区域 */
.coupon-section {
    padding: 15px;
    background: #f5f5f5;
}

.coupon-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    margin-bottom: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.coupon-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #ff4757;
}

.coupon-left {
    flex: 1;
}

.coupon-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.coupon-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.coupon-price {
    display: flex;
    align-items: baseline;
    margin-bottom: 8px;
}

.price-num {
    font-size: 32px;
    font-weight: 700;
    color: #ff4757;
    line-height: 1;
}

.price-unit {
    font-size: 16px;
    color: #ff4757;
    margin-left: 2px;
}

.coupon-desc {
    font-size: 12px;
    color: #999;
    line-height: 1.4;
}

.coupon-right {
    margin-left: 20px;
}

.receive-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.receive-btn:hover {
    background: #ff3838;
    transform: translateY(-1px);
}

/* 医生列表 */
.doctor-section {
    background: white;
    margin: 0 15px 15px;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.doctor-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.doctor-item:last-child {
    border-bottom: none;
}

.doctor-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.doctor-info {
    flex: 1;
}

.doctor-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.doctor-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 2px;
}

.doctor-specialty {
    font-size: 12px;
    color: #999;
}

.consult-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 14px;
    cursor: pointer;
}

/* 预约表单 */
.appointment-section {
    background: white;
    margin: 0 15px 15px;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.form-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 15px;
}

.appointment-form input,
.appointment-form select,
.appointment-form textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.appointment-form input:focus,
.appointment-form select:focus,
.appointment-form textarea:focus {
    outline: none;
    border-color: #ff4757;
    background: white;
}

.appointment-form textarea {
    height: 80px;
    resize: vertical;
}

.submit-btn {
    width: 100%;
    background: #ff4757;
    color: white;
    border: none;
    padding: 16px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background: #ff3838;
}

.form-note {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ff4757;
}

.form-note p {
    font-size: 12px;
    color: #666;
    line-height: 1.5;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: white;
    display: flex;
    border-top: 1px solid #e0e0e0;
    z-index: 1000;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.nav-item.active {
    color: #ff4757;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 12px;
}

/* 个人中心 */
.profile-header {
    padding: 30px 20px;
    background: white;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #f0f0f0;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #999;
}

.username {
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.menu-list {
    background: white;
    margin: 0 15px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.menu-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background: #f8f9fa;
}

.menu-icon {
    font-size: 18px;
    margin-right: 15px;
    color: #ff4757;
}

.menu-item span {
    flex: 1;
    font-size: 16px;
    color: #333;
}

.arrow {
    color: #ccc;
    font-size: 16px;
}

/* 页面头部 */
.page-header {
    padding: 15px 20px;
    background: white;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.back-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #333;
    cursor: pointer;
    margin-right: 15px;
    padding: 5px;
}

.page-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 标签页 */
.tabs {
    display: flex;
    background: white;
    margin: 0 15px;
    border-radius: 12px 12px 0 0;
    overflow: hidden;
}

.tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-size: 16px;
    color: #666;
    transition: all 0.3s ease;
}

.tab.active {
    color: #ff4757;
    border-bottom-color: #ff4757;
    background: #fff5f5;
}

/* 优惠券详情 */
.coupon-detail-list {
    padding: 0 15px 20px;
}

.coupon-detail {
    background: white;
    border-radius: 0 0 12px 12px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.coupon-detail:first-child {
    border-radius: 12px;
}

.coupon-detail-content {
    padding: 25px;
    position: relative;
}

.coupon-detail h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    padding-right: 80px;
}

.validity {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
}

.coupon-price-large {
    position: absolute;
    top: 25px;
    right: 25px;
    display: flex;
    align-items: baseline;
}

.price-large {
    font-size: 48px;
    font-weight: 700;
    color: #ff4757;
    line-height: 1;
}

.unit {
    font-size: 18px;
    color: #ff4757;
    margin-left: 4px;
}

.usage-rules {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.usage-rules h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.usage-rules p {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 4px;
}

.usage-rules p:last-child {
    margin-bottom: 0;
}

.use-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.use-btn:hover {
    background: #ff3838;
    transform: translateY(-1px);
}

/* 预约列表 */
.appointment-list {
    padding: 15px;
}

.appointment-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.appointment-info h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.appointment-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.appointment-status {
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.appointment-status.pending {
    background: #fff3cd;
    color: #856404;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state p {
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .banner {
        height: 240px;
    }
    
    .banner h1 {
        font-size: 20px;
    }
    
    .coupon-item {
        padding: 15px;
    }
    
    .price-num {
        font-size: 28px;
    }
    
    .price-large {
        font-size: 40px;
    }
}
