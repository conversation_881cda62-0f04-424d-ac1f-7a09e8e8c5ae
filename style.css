* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background: #f7f8fa;
    padding-bottom: 50px;
    color: #323233;
}

.page {
    min-height: 100vh;
    background: #f7f8fa;
}

.hidden {
    display: none;
}

/* Banner */
.banner {
    position: relative;
    height: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.banner-content {
    text-align: center;
    color: white;
    z-index: 2;
}

.hospital-logo img {
    width: 80px;
    height: 80px;
    margin-bottom: 16px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.banner h1 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 12px;
    text-shadow: 0 2px 8px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.subtitle {
    font-size: 18px;
    opacity: 0.95;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 优惠券区域 */
.coupon-section {
    padding: 16px;
    background: #f7f8fa;
}

.coupon-desc {
    font-size: 13px;
    color: #969799;
    line-height: 1.4;
    margin-top: 4px;
}

/* 页面切换动画 */
.page {
    transition: all 0.3s ease-in-out;
}

.page.hidden {
    opacity: 0;
    transform: translateX(100%);
}

/* Vant组件自定义样式 */
.van-cell-group--inset {
    margin: 16px;
}

.van-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.van-button--primary:active {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 医生区域 */
.doctor-section {
    margin: 16px 0;
}

/* 预约区域 */
.appointment-section {
    margin: 16px 0;
}

.form-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
}

.submit-section {
    padding: 16px;
}

/* 个人中心 */
.username {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
    margin-left: 12px;
}

/* 预约详情 */
.appointment-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
}

.appointment-info {
    font-size: 14px;
    color: #969799;
    line-height: 1.4;
}

.appointment-info div {
    margin-bottom: 4px;
}

.appointment-info div:last-child {
    margin-bottom: 0;
}

/* 优惠券列表 */
.coupon-list {
    padding: 16px;
}

/* 自定义Vant主题色 */
:root {
    --van-primary-color: #667eea;
    --van-success-color: #07c160;
    --van-warning-color: #ff976a;
    --van-danger-color: #ee0a24;
    --van-text-color: #323233;
    --van-text-color-2: #646566;
    --van-text-color-3: #969799;
    --van-background-color: #f7f8fa;
    --van-background-color-light: #fafafa;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .banner {
        height: 240px;
    }

    .banner h1 {
        font-size: 24px;
    }

    .hospital-logo img {
        width: 60px;
        height: 60px;
    }
}
