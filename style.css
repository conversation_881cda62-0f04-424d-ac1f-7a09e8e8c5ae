* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background: #f7f8fa;
    padding-bottom: 50px;
    color: #323233;
}

.page {
    min-height: 100vh;
    background: #f7f8fa;
}

.hidden {
    display: none;
}

/* Banner */
.banner {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, #6c7ae0 0%, #7c4dff 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.banner-content {
    text-align: center;
    color: white;
    z-index: 2;
}

.hospital-logo img {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    border-radius: 50%;
    background: white;
    padding: 10px;
}

.banner h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

/* 优惠券区域 */
.coupon-section {
    padding: 15px;
    background: #f5f5f5;
}

.coupon-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.coupon-left {
    flex: 1;
}

.coupon-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.coupon-subtitle {
    font-size: 12px;
    color: #999;
    line-height: 1.4;
}

.coupon-right {
    text-align: right;
}

.coupon-price {
    font-size: 24px;
    font-weight: bold;
    color: #6c7ae0;
    margin-bottom: 8px;
}

.receive-btn {
    background: #6c7ae0;
    color: white;
    border: none;
    padding: 6px 16px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
}

/* 医生区域 */
.doctor-section {
    background: white;
    margin: 0 15px 15px;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.doctor-item {
    display: flex;
    align-items: center;
}

.doctor-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 12px;
}

.doctor-info {
    flex: 1;
}

.doctor-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.doctor-title {
    font-size: 12px;
    color: #999;
}

.consult-btn {
    background: #6c7ae0;
    color: white;
    border: none;
    padding: 6px 16px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
}

/* 预约区域 */
.appointment-section {
    background: white;
    margin: 0 15px 15px;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.form-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 15px;
}

.appointment-form input,
.appointment-form select {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    background: #fafafa;
}

.appointment-form input:focus,
.appointment-form select:focus {
    outline: none;
    border-color: #6c7ae0;
    background: white;
}

.submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #6c7ae0 0%, #7c4dff 100%);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: white;
    display: flex;
    border-top: 1px solid #e0e0e0;
    z-index: 1000;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.nav-item.active {
    color: #6c7ae0;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 12px;
}

/* 个人中心 */
.profile-header {
    padding: 30px 20px;
    background: white;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
    overflow: hidden;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.username {
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.menu-list {
    background: white;
    margin: 0 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.menu-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background: #f8f9fa;
}

.menu-icon {
    font-size: 18px;
    margin-right: 15px;
    color: #ff4757;
}

.menu-item span {
    flex: 1;
    font-size: 16px;
    color: #333;
}

.arrow {
    color: #ccc;
    font-size: 16px;
}

/* 页面头部 */
.page-header {
    padding: 15px 20px;
    background: white;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.back-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #333;
    cursor: pointer;
    margin-right: 15px;
    padding: 5px;
}

.page-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #999;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 20px;
}

.start-appointment-btn {
    background: none;
    border: 2px solid #ff4757;
    color: #ff4757;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
}

/* 标签页 */
.tabs {
    display: flex;
    background: white;
    margin: 0 15px;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-size: 16px;
    color: #666;
    transition: all 0.3s ease;
}

.tab.active {
    color: #ff4757;
    border-bottom-color: #ff4757;
    background: #fff5f5;
}

/* 优惠券详情 */
.coupon-detail-list {
    padding: 0 15px 20px;
}

.coupon-detail {
    background: white;
    border-radius: 0 0 8px 8px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.coupon-detail:first-child {
    border-radius: 8px;
}

.coupon-detail-content {
    padding: 20px;
    position: relative;
}

.coupon-detail h3 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    padding-right: 80px;
}

.validity {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
}

.coupon-price-large {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: baseline;
}

.price-large {
    font-size: 36px;
    font-weight: bold;
    color: #ff4757;
    line-height: 1;
}

.unit {
    font-size: 14px;
    color: #ff4757;
    margin-left: 2px;
}

.usage-rules {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

.usage-rules h4 {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    margin-bottom: 6px;
}

.usage-rules p {
    font-size: 11px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 2px;
}

.usage-rules p:last-child {
    margin-bottom: 0;
}

.use-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
}

/* 页面切换动画 */
.page {
    transition: all 0.3s ease-in-out;
}

.page.hidden {
    display: none;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .banner {
        height: 240px;
    }

    .banner h1 {
        font-size: 20px;
    }

    .hospital-logo img {
        width: 60px;
        height: 60px;
    }

    .coupon-item {
        padding: 12px;
    }

    .coupon-price {
        font-size: 20px;
    }
}
