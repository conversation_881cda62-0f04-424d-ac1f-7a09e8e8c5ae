
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>徽健通</title>
    <!-- Vant CSS -->
    <link rel="stylesheet" href="https://fastly.jsdelivr.net/npm/vant@4/lib/index.css" />
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 首页 -->
    <div class="page" id="homePage">
        <!-- Banner图 -->
        <div class="banner">
            <div class="banner-content">
                <div class="hospital-logo">
                    <img src="logo.svg" alt="医院logo">
                </div>
                <h1>徽健通益肤皮肤病</h1>
                <p class="subtitle">守肤惠民公益行动</p>
            </div>
        </div>

        <!-- 优惠券领取 -->
        <div class="coupon-section">
            <div class="coupon-item">
                <div class="coupon-left">
                    <div class="coupon-title">皮肤三维皮肤检测仪</div>
                    <div class="coupon-subtitle">原价检查费用</div>
                </div>
                <div class="coupon-right">
                    <div class="coupon-price">0元</div>
                    <button class="receive-btn" @click="receiveCoupon(1)">立即领取</button>
                </div>
            </div>

            <div class="coupon-item">
                <div class="coupon-left">
                    <div class="coupon-title">美国进口308光斑</div>
                    <div class="coupon-subtitle">价值高达360元，免费体验6个（限初诊）</div>
                </div>
                <div class="coupon-right">
                    <div class="coupon-price">78元</div>
                    <button class="receive-btn" @click="receiveCoupon(2)">立即领取</button>
                </div>
            </div>

            <div class="coupon-item">
                <div class="coupon-left">
                    <div class="coupon-title">美国进口308光斑</div>
                    <div class="coupon-subtitle">价值高达360元，免费体验6个（限初诊）</div>
                </div>
                <div class="coupon-right">
                    <div class="coupon-price">半价</div>
                    <button class="receive-btn" @click="receiveCoupon(3)">立即领取</button>
                </div>
            </div>
        </div>

        <!-- 医生介绍 -->
        <div class="doctor-section">
            <div class="doctor-item">
                <img src="doctor1.svg" alt="医生头像" class="doctor-avatar">
                <div class="doctor-info">
                    <h3 class="doctor-name">张主任</h3>
                    <p class="doctor-title">皮肤科主任医师 · 擅长：白癜风、银屑病等</p>
                </div>
                <button class="consult-btn" @click="consultDoctor">咨询</button>
            </div>
        </div>

        <!-- 预约表单 -->
        <div class="appointment-section">
            <div class="form-title">患者信息-专家会诊预约</div>
            <form class="appointment-form" @submit.prevent="onSubmit">
                <div class="form-group">
                    <input type="text" placeholder="请输入姓名" v-model="form.name" required>
                </div>
                <div class="form-group">
                    <input type="tel" placeholder="请输入手机号" v-model="form.phone" required>
                </div>
                <div class="form-group">
                    <select v-model="form.condition" required>
                        <option value="">请选择病情</option>
                        <option value="白癜风">白癜风</option>
                        <option value="银屑病">银屑病</option>
                        <option value="湿疹">湿疹</option>
                        <option value="荨麻疹">荨麻疹</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <button type="submit" class="submit-btn">提交预约</button>
            </form>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item active" @click="showPage('home', $event)">
            <div class="nav-icon">🏠</div>
            <span>首页</span>
        </div>
        <div class="nav-item" @click="showPage('profile', $event)">
            <div class="nav-icon">👤</div>
            <span>个人中心</span>
        </div>
    </div>

    <!-- 个人中心页面 -->
    <div class="page hidden" id="profilePage">
        <div class="profile-header">
            <div class="avatar">
                <img src="https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg" alt="头像">
            </div>
            <span class="username">微信用户</span>
        </div>

        <div class="menu-list">
            <div class="menu-item" @click="showMyAppointments">
                <div class="menu-icon">📅</div>
                <span>我的预约</span>
                <span class="arrow">></span>
            </div>
            <div class="menu-item" @click="showMyCoupons">
                <div class="menu-icon">🎫</div>
                <span>我的优惠券</span>
                <span class="arrow">></span>
            </div>
        </div>
    </div>

    <!-- 我的预约页面 -->
    <div class="page hidden" id="appointmentsPage">
        <div class="page-header">
            <button class="back-btn" @click="goBack">←</button>
            <h2>我的预约</h2>
        </div>

        <div class="empty-state">
            <div class="empty-icon">📄</div>
            <p>暂无预约记录</p>
            <button class="start-appointment-btn">立即开始挂号</button>
        </div>
    </div>

    <!-- 我的优惠券页面 -->
    <div class="page hidden" id="couponsPage">
        <div class="page-header">
            <button class="back-btn" @click="goBack">←</button>
            <h2>我的优惠券</h2>
        </div>

        <div class="tabs">
            <div class="tab active" @click="switchTab('unused', $event)">未使用</div>
            <div class="tab" @click="switchTab('used', $event)">已使用</div>
        </div>

        <div class="coupon-detail-list" id="unusedCoupons">
            <div class="coupon-detail">
                <div class="coupon-detail-content">
                    <h3>皮肤镜专业检测</h3>
                    <p class="validity">有效期至：2025-08-31</p>
                    <div class="coupon-price-large">
                        <span class="price-large">60</span>
                        <span class="unit">元</span>
                    </div>
                    <div class="usage-rules">
                        <h4>使用规则</h4>
                        <p>1.价值高达120元；</p>
                        <p>2.本次援助最终解释权归浙江益肤所有。</p>
                    </div>
                    <button class="use-btn" @click="useCoupon(0)">立即使用</button>
                </div>
            </div>

            <div class="coupon-detail">
                <div class="coupon-detail-content">
                    <h3>白癜风6维32项基础病因筛查</h3>
                    <p class="validity">有效期至：2025-08-31</p>
                    <div class="coupon-price-large">
                        <span class="price-large">0</span>
                        <span class="unit">元</span>
                    </div>
                    <div class="usage-rules">
                        <h4>使用规则</h4>
                        <p>本次援助最终解释权归浙江益肤所有。</p>
                    </div>
                    <button class="use-btn" @click="useCoupon(1)">立即使用</button>
                </div>
            </div>

            <div class="coupon-detail">
                <div class="coupon-detail-content">
                    <h3>美国进口 308 光斑</h3>
                    <p class="validity">有效期至：2025-08-31</p>
                    <div class="coupon-price-large">
                        <span class="price-large">0</span>
                        <span class="unit">元</span>
                    </div>
                    <div class="usage-rules">
                        <h4>使用规则</h4>
                        <p>1.价值高达360元；</p>
                        <p>2.免费体验 6 个（限初诊）；</p>
                        <p>3.本次援助最终解释权归浙江益肤所有。</p>
                    </div>
                    <button class="use-btn" @click="useCoupon(2)">立即使用</button>
                </div>
            </div>
        </div>

        <div class="coupon-detail-list hidden" id="usedCoupons">
            <div class="empty-state">
                <p>暂无已使用的优惠券</p>
            </div>
        </div>
    </div>



    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Vant JS -->
    <script src="https://fastly.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
    <script src="script.js"></script>
</body>
</html>

