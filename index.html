
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浙江益肤皮肤病医院</title>
    <!-- Vant CSS -->
    <link rel="stylesheet" href="https://fastly.jsdelivr.net/npm/vant@4/lib/index.css" />
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 首页 -->
    <div class="page" id="homePage">
        <!-- Banner图 -->
        <div class="banner">
            <div class="banner-content">
                <div class="hospital-logo">
                    <img src="logo.svg" alt="医院logo">
                </div>
                <h1>浙江益肤皮肤病医院</h1>
                <p class="subtitle">行肤惠民公益行动</p>
            </div>
        </div>

        <!-- 优惠券领取 -->
        <div class="coupon-section">
            <van-cell-group inset>
                <van-cell
                    title="皮肤三维皮肤检测仪"
                    label="原价检查费用"
                    @click="receiveCoupon(1)">
                    <template #value>
                        <div style="text-align: right;">
                            <div style="font-size: 24px; font-weight: bold; color: #667eea;">0元</div>
                            <van-button type="primary" size="small" round>立即领取</van-button>
                        </div>
                    </template>
                </van-cell>

                <van-cell
                    title="美国进口308光斑"
                    label="价值高达360元，免费体验6个（限初诊）"
                    @click="receiveCoupon(2)">
                    <template #value>
                        <div style="text-align: right;">
                            <div style="font-size: 24px; font-weight: bold; color: #667eea;">78元</div>
                            <van-button type="primary" size="small" round>立即领取</van-button>
                        </div>
                    </template>
                </van-cell>

                <van-cell
                    title="美国进口308光斑"
                    label="价值高达360元，免费体验6个（限初诊）"
                    @click="receiveCoupon(3)">
                    <template #value>
                        <div style="text-align: right;">
                            <div style="font-size: 24px; font-weight: bold; color: #667eea;">半价</div>
                            <van-button type="primary" size="small" round>立即领取</van-button>
                        </div>
                    </template>
                </van-cell>
            </van-cell-group>
        </div>

        <!-- 医生介绍 -->
        <div class="doctor-section">
            <van-cell-group inset>
                <van-cell
                    title="张主任"
                    label="皮肤科主任医师 · 擅长：白癜风、银屑病等"
                    is-link
                    @click="consultDoctor">
                    <template #icon>
                        <van-image
                            width="50"
                            height="50"
                            round
                            src="doctor1.svg"
                            fit="cover"
                        />
                    </template>
                    <template #right-icon>
                        <van-button type="primary" size="small" round>咨询</van-button>
                    </template>
                </van-cell>
            </van-cell-group>
        </div>

        <!-- 预约表单 -->
        <div class="appointment-section">
            <van-cell-group inset>
                <van-cell title="患者信息-专家会诊预约" title-class="form-title" />
            </van-cell-group>

            <van-form @submit="onSubmit">
                <van-cell-group inset>
                    <van-field
                        v-model="form.name"
                        name="name"
                        label="姓名"
                        placeholder="请输入姓名"
                        :rules="[{ required: true, message: '请填写姓名' }]"
                    />
                    <van-field
                        v-model="form.phone"
                        name="phone"
                        label="手机号"
                        placeholder="请输入手机号"
                        :rules="[{ required: true, message: '请填写手机号' }]"
                    />
                    <van-field
                        v-model="form.condition"
                        is-link
                        readonly
                        name="condition"
                        label="病情"
                        placeholder="请选择病情"
                        @click="showConditionPicker = true"
                        :rules="[{ required: true, message: '请选择病情' }]"
                    />
                    <van-field
                        v-model="form.description"
                        rows="3"
                        autosize
                        label="病情描述"
                        type="textarea"
                        placeholder="请描述您的病情（选填）"
                    />
                </van-cell-group>

                <div class="submit-section">
                    <van-button round block type="primary" native-type="submit">
                        提交预约
                    </van-button>
                </div>
            </van-form>

            <div style="margin: 16px;">
                <van-notice-bar
                    left-icon="info-o"
                    text="温馨提示：我院严格执行国家收费标准，拒绝任何乱收费现象，病情不同，治疗方案不同，费用不同，我院医生根据您的病情为您制定个性化的治疗方案。"
                    wrapable
                    background="#fff7e6"
                    color="#ed6a0c"
                />
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" @change="onTabChange">
        <van-tabbar-item icon="home-o" name="home">首页</van-tabbar-item>
        <van-tabbar-item icon="user-o" name="profile">个人中心</van-tabbar-item>
    </van-tabbar>

    <!-- 个人中心页面 -->
    <div class="page hidden" id="profilePage">
        <van-cell-group inset>
            <van-cell center>
                <template #icon>
                    <van-image
                        round
                        width="60"
                        height="60"
                        src="https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg"
                    />
                </template>
                <template #title>
                    <span class="username">微信用户</span>
                </template>
            </van-cell>
        </van-cell-group>

        <van-cell-group inset>
            <van-cell title="我的预约" icon="calendar-o" is-link @click="showMyAppointments" />
            <van-cell title="我的优惠券" icon="coupon-o" is-link @click="showMyCoupons" />
        </van-cell-group>
    </div>

    <!-- 我的预约页面 -->
    <div class="page hidden" id="appointmentsPage">
        <van-nav-bar
            title="我的预约"
            left-text="返回"
            left-arrow
            @click-left="goBack"
        />

        <van-cell-group inset>
            <van-cell>
                <template #title>
                    <div class="appointment-title">皮肤检查</div>
                </template>
                <template #label>
                    <div class="appointment-info">
                        <div>预约时间：2024-01-15 14:00</div>
                        <div>医生：张主任</div>
                    </div>
                </template>
                <template #right-icon>
                    <van-tag type="warning">待就诊</van-tag>
                </template>
            </van-cell>
        </van-cell-group>
    </div>

    <!-- 我的优惠券页面 -->
    <div class="page hidden" id="couponsPage">
        <van-nav-bar
            title="我的优惠券"
            left-text="返回"
            left-arrow
            @click-left="goBack"
        />

        <van-tabs v-model:active="couponTab" @change="onCouponTabChange">
            <van-tab title="未使用" name="unused">
                <div class="coupon-list">
                    <van-coupon-cell
                        :coupons="unusedCoupons"
                        :chosen-coupon="0"
                        @click="useCoupon"
                    />
                </div>
            </van-tab>
            <van-tab title="已使用" name="used">
                <van-empty description="暂无已使用的优惠券" />
            </van-tab>
        </van-tabs>
    </div>

    <!-- 选择器 -->
    <van-popup v-model:show="showConditionPicker" position="bottom">
        <van-picker
            :columns="conditionOptions"
            @confirm="onConditionConfirm"
            @cancel="showConditionPicker = false"
        />
    </van-popup>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Vant JS -->
    <script src="https://fastly.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
    <script src="script.js"></script>
</body>
</html>

